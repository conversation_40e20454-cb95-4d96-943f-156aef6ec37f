import asyncio
from typing import List, Dict, Any, AsyncGenerator, Optional
from openai import AsyncAzureOpenAI

from app.llm.base import (
    LLMProvider, 
    ChatMessage, 
    LLMResponse, 
    StreamingChunk,
    LLMProviderFactory
)
from app.core.config import settings
from app.core.exceptions import LLMError


class AzureOpenAIProvider(LLMProvider):
    """Azure OpenAI LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Initialize Azure OpenAI client
        api_key = config.get("api_key") or settings.azure_openai_api_key
        if not api_key:
            raise LLMError("Azure OpenAI API key is required")
        
        endpoint = config.get("endpoint") or settings.azure_openai_endpoint
        if not endpoint:
            raise LLMError("Azure OpenAI endpoint is required")
        
        api_version = config.get("api_version") or settings.azure_openai_api_version
        deployment = config.get("deployment") or settings.azure_openai_deployment
        if not deployment:
            raise LLMError("Azure OpenAI deployment name is required")
        
        self.client = AsyncAzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=api_version
        )
        self.deployment = deployment
        self.default_temperature = config.get("temperature", 0.7)
        self.default_max_tokens = config.get("max_tokens", 2000)
    
    def _convert_messages(self, messages: List[ChatMessage]) -> List[Dict[str, str]]:
        """Convert internal message format to Azure OpenAI format."""
        azure_messages = []
        for msg in messages:
            azure_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        return azure_messages
    
    async def generate_response(
        self,
        messages: List[ChatMessage],
        **kwargs
    ) -> LLMResponse:
        """
        Generate a complete response from Azure OpenAI.
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Returns:
            LLMResponse: Complete response from Azure OpenAI
            
        Raises:
            LLMError: If the API call fails
        """
        try:
            # Prepare parameters
            azure_messages = self._convert_messages(messages)
            
            # Use provided parameters or defaults
            temperature = kwargs.get("temperature", self.default_temperature)
            max_tokens = kwargs.get("max_tokens", self.default_max_tokens)
            
            # Make API call
            response = await self.client.chat.completions.create(
                model=self.deployment,  # Use deployment name for Azure
                messages=azure_messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=False
            )
            
            # Extract response data
            choice = response.choices[0]
            content = choice.message.content
            
            # Prepare usage information
            usage = None
            if hasattr(response, 'usage') and response.usage:
                usage = {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            
            return LLMResponse(
                content=content,
                usage=usage,
                finish_reason=choice.finish_reason,
                metadata={
                    "deployment": self.deployment,
                    "provider": "azure_openai"
                }
            )
            
        except Exception as e:
            if "azure" in str(type(e)).lower() or "openai" in str(type(e)).lower():
                raise LLMError(f"Azure OpenAI API error: {str(e)}")
            raise LLMError(f"Unexpected error in Azure OpenAI provider: {str(e)}")
    
    async def generate_stream(
        self,
        messages: List[ChatMessage],
        **kwargs
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Generate a streaming response from Azure OpenAI.
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters
            
        Yields:
            StreamingChunk: Streaming response chunks
            
        Raises:
            LLMError: If the API call fails
        """
        try:
            # Prepare parameters
            azure_messages = self._convert_messages(messages)
            
            temperature = kwargs.get("temperature", self.default_temperature)
            max_tokens = kwargs.get("max_tokens", self.default_max_tokens)
            
            # Make streaming API call
            stream = await self.client.chat.completions.create(
                model=self.deployment,  # Use deployment name for Azure
                messages=azure_messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            # Process streaming chunks
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    # Check if this is the final chunk
                    is_complete = choice.finish_reason is not None
                    
                    # Get content delta
                    content = ""
                    if choice.delta and choice.delta.content:
                        content = choice.delta.content
                    
                    # Prepare metadata
                    metadata = {
                        "deployment": self.deployment,
                        "provider": "azure_openai"
                    }
                    
                    if is_complete and choice.finish_reason:
                        metadata["finish_reason"] = choice.finish_reason
                    
                    yield StreamingChunk(
                        content=content,
                        is_complete=is_complete,
                        metadata=metadata
                    )
                    
                    if is_complete:
                        break
                        
        except Exception as e:
            if "azure" in str(type(e)).lower() or "openai" in str(type(e)).lower():
                raise LLMError(f"Azure OpenAI API error: {str(e)}")
            raise LLMError(f"Unexpected error in Azure OpenAI streaming: {str(e)}")
    
    async def validate_connection(self) -> bool:
        """
        Validate Azure OpenAI connection by making a simple API call.
        
        Returns:
            bool: True if connection is valid
        """
        try:
            # Make a minimal API call to test connection
            test_messages = [ChatMessage(role="user", content="Hello")]
            
            response = await self.client.chat.completions.create(
                model=self.deployment,
                messages=self._convert_messages(test_messages),
                max_tokens=1,
                temperature=0
            )
            
            return True
            
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Azure OpenAI model information."""
        info = super().get_model_info()
        info.update({
            "azure_endpoint": str(self.client._base_url),
            "deployment": self.deployment,
            "default_temperature": self.default_temperature,
            "default_max_tokens": self.default_max_tokens
        })
        return info


# Register the Azure OpenAI provider
LLMProviderFactory.register_provider("azure_openai", AzureOpenAIProvider)


def create_azure_openai_provider(config: Optional[Dict[str, Any]] = None) -> AzureOpenAIProvider:
    """
    Convenience function to create an Azure OpenAI provider.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        AzureOpenAIProvider: Configured Azure OpenAI provider instance
    """
    if config is None:
        config = {
            "deployment": settings.azure_openai_deployment,
            "api_key": settings.azure_openai_api_key,
            "endpoint": settings.azure_openai_endpoint,
            "api_version": settings.azure_openai_api_version,
            "temperature": 0.7,
            "max_tokens": 2000
        }
    
    return AzureOpenAIProvider(config)
